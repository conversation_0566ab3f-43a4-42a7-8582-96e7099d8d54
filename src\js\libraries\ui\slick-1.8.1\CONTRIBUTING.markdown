## Submitting issues

### Test case required

**All bug reports and problem issues require a jsFiddle**.   
[Please fork this JSFiddle as a baseline](http://jsfiddle.net/simeydotme/fmo50w7n/).

+ A test case clearly demonstrates the bug or issue.
+ It contains the bare minimum HTML, CSS, and JavaScript required to demonstrate the bug.
+ Assets are not minified (we cannot debug .min.js files).
+ A link to your production site is **not** a reduced test case.

Providing a test case is the best way to get your issue addressed. Without a test case; your issue may be closed.  
You must provide a clear and simple way to reproduce the issue with the provided fiddle.

## Support

Although Slick is designed to be super easy to use, there's always things that can go wrong, especially when interacting with other plugins and complicated websites. If you are **having problems getting <PERSON>lick to work**, or can't figure something out, **this repository**'s issues section **is not the place to ask about it**.  

I have set up a subreddit for Slick discussion at [reddit.com/r/slickcarousel](http://www.reddit.com/r/slickcarousel/).  
And a gitter chatroom here: [gitter.im/kenwheeler/slick](https://gitter.im/kenwheeler/slick).  
And finally you can ask the world for help, and find out if anyone else has your problem at [stackoverflow](http://stackoverflow.com/search?q=slick+carousel).

Please use these forums as an area to discuss Slick and any issues you may have.

## Feature Requests

If you would like to request or propose a feature, please do so in the the Slick subreddit or gitter chat:  
[reddit.com/r/slickcarousel](http://www.reddit.com/r/slickcarousel/) / [gitter.im/kenwheeler/slick](https://gitter.im/kenwheeler/slick).

## Pull requests

**Contributions are welcome**! That said, please *be prepared to edit* your pull request at request, and provide a jsFiddle of your fork working interoperably with every "mode" that Slick supports, and with varying numbers of slides, slidesToShow & slidesToScroll. Failure to do so will result in your pull request being closed.

*Please note that while Slick is open source, this is still my baby, and by submitting a pull request you are authorizing me to edit or modify it in any way shape or form. You will be listed in Github as a contributor, but I have and will continue to steer the direction of this project.*
