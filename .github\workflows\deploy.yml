name: Deploy WEBAPP via FTP

on:
  # Deploy after successful release
  workflow_run:
    workflows: ["Release"]
    types:
      - completed
    branches:
      - main
  # Allow manual deployment
  workflow_dispatch:

jobs:
  ftp-deploy:
    runs-on: ubuntu-latest
    # Only deploy if the Release workflow succeeded or if manually triggered
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Only fetch the latest commit, avoids bringing .git/

      - name: Verify required files
        run: |
          echo "Checking critical files..."
          if [ ! -f "index.html" ]; then
            echo "❌ Error: index.html not found"
            exit 1
          fi
          if [ ! -f "sw.js" ]; then
            echo "❌ Error: sw.js not found"
            exit 1
          fi
          if [ ! -f "manifest.json" ]; then
            echo "❌ Error: manifest.json not found"
            exit 1
          fi
          echo "✅ All critical files are present"

      - name: Show deployment information
        run: |
          echo "🚀 Starting deployment..."
          echo "Commit: ${{ github.sha }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Workflow: ${{ github.workflow }}"

      - name: Deploy via FTP
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: ${{ secrets.FTP_SERVER }}
          username: ${{ secrets.FTP_USERNAME }}
          password: ${{ secrets.FTP_PASSWORD }}
          server-dir: /clashstrategic.great-site.net/htdocs/
          local-dir: ./
          exclude: |
            .git/
            **/.git/**
            **/.gitignore
            **/.github/**
            **/node_modules/**
            **/tests/**
            **/.env*
            **/update-sw-version.js
            **/.releaserc*
            **/eslint.config.*
            **/package-lock.json
            **/package.json
            **/.vscode/**
            **/.clinerules/**
            **/README*.md
            **/CHANGELOG.md
            **/*.log
            **/.DS_Store
            **/Thumbs.db
            **/.npmrc
            **/.editorconfig
            **/babel.config.js
            **/jest.config.js
            **/playwright.config.js
            **/LICENSE

      - name: Verify deployment
        run: |
          echo "✅ Deployment completed successfully"
          echo "🌐 Website: https://clashstrategic.great-site.net/"
