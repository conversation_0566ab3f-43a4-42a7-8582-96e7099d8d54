name: Jest Unit Tests

on:
  pull_request:
    branches: [ main, master ]
    paths:
      - '**.js'
      - '**.json'
      - 'package*.json'
      - 'jest.config.*'
      - '.github/workflows/jest.yml'

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run Jest tests
      run: npm test
      
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: jest-results-node-${{ matrix.node-version }}
        path: |
          coverage/
          junit.xml
        retention-days: 30
        
    - name: Comment PR with test results
      uses: actions/github-script@v7
      if: always() && github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          const path = require('path');
          
          // Try to read test results if available
          let testSummary = '## 🧪 Jest Unit Tests Results\n\n';
          testSummary += `**Node.js Version:** ${{ matrix.node-version }}\n`;
          testSummary += `**Status:** ${{ job.status === 'success' && '✅ Passed' || '❌ Failed' }}\n\n`;
          
          if ('${{ job.status }}' === 'success') {
            testSummary += '✅ All unit tests passed successfully!';
          } else {
            testSummary += '❌ Some unit tests failed. Please check the workflow logs for details.';
          }
          
          // Only comment on the first matrix job to avoid spam
          if ('${{ matrix.node-version }}' === '18') {
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: testSummary
            });
          }
