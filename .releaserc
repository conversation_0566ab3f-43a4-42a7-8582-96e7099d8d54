{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/changelog", ["./update-sw-version.js", {}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json", "sw.js"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}