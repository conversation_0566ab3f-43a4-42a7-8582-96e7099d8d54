#menu {
    background-color: transparent;
    text-align: center;
    position: fixed;
    margin: auto;
    bottom: 0;
    left: 0%;
    right: 0%;
    z-index: 90;
    display: flex;
    justify-content: space-between;
    width: var(--width-body);
}

.a_menu {
    background: var(--cs-color-DeepBlue);
    position: relative;
    filter: brightness(80%);
    background-size: cover;
    margin: auto;
    display: inline-block;
    width: 25%;
    box-shadow: 0px 0px 5px 0px var(--cs-color-DeepBlue);
    padding-left: 10px;
    padding-right: 10px;
}

.a_menu:hover {
    background: var(--Brown);
    filter: brightness(115%);
    box-shadow: 0 0 8px var(--cs-color-DeepBlue);
    transform: scale(1.05);
}

.a_menu_select {
    background: var(--Brown);
    filter: brightness(110%);
    transform: scale(1.05);
    z-index: 20;
}

.img_menu {
    height: 50px;
    width: 55px;
    margin-bottom: -3px;
    filter: brightness(115%);
}

#span_new_mesasge {
    position: absolute;
    font-size: var(--fs-x-small);
    margin-left: -15px;
    margin-top: -5px;
}