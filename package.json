{"name": "clash-strategic-webapp", "devDependencies": {"@babel/preset-env": "^7.27.2", "@eslint/js": "^9.21.0", "@playwright/test": "^1.53.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.2", "@types/node": "^24.0.4", "babel-jest": "^30.0.2", "eslint": "^9.21.0", "eslint-config-jquery": "^3.0.2", "eslint-plugin-jquery": "^1.5.1", "globals": "^16.0.0", "http-server": "^14.1.1", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jquery": "^3.7.1", "semantic-release": "^24.2.4"}, "scripts": {"test": "jest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:local": "PLAYWRIGHT_BASE_URL=http://localhost/clash-strategic-webapp playwright test"}, "version": "1.2.1"}