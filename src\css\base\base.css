* {
    margin: 0;
    padding: 0;
}

html {
    box-sizing: border-box;
}

*,
*::before,
*::after {
    box-sizing: inherit;
}

body {
    font-family: 'Poppins';
    font-weight: 600;
    font-style: normal;
    color: var(--cs-color-LightGrey);
    line-height: 1.25rem;
    background: var(--cs-color-LightGrey);
    width: var(--width-body, 100%);
}

p,
span,
label,
strong,
em,
li,
td,
th,
textarea,
input,
legend,
button,
select {
    font-size: var(--fs-medium);
    font-family: inherit;
    font-weight: inherit;
}

h1 {
    font-size: var(--fs-xxx-large);
    font-weight: 900;
    margin: 1.25rem 0;
}

h2 {
    font-weight: 800;
    font-size: var(--fs-x-large);
    margin: 1rem 0;
}

h3 {
    font-weight: 700;
    font-size: var(--fs-xxx-medium);
    margin: 0.75rem 0;
}

h1,
h2,
h3 {
    color: var(--cs-color-DeepBlue);
    text-align: center;
}